<template>
    <van-dialog
        v-model="show"
        @close="handleClose"
        v-bind="$attrs"
        class="common_dialog"
        :show-cancel-button="showRejectButton"
        :show-confirm-button="showConfirmButton"
        :before-close="handleBeforeClose"
        :confirm-button-text="confirmButtonText"
        :cancel-button-text="rejectButtonText"
        :message-align="messageAlign"
        @confirm="handleConfirm"
        @cancel="handleReject"
        :closeOnClickOverlay="closeOnClickOverlay"
        :close-on-popstate="false"
        :key="currentDialogId"
    >
        <div class="common_dialog-title">{{ title }}</div>
        <div class="common_dialog-content" :class="[`align_${messageAlign}`]">
            <slot><p v-html="message" class="common_dialog-message"></p></slot>
        </div>
        <div class="cancel_button" v-if="showCancelButton" @click="handelCancelButton">
            <van-icon name="close" size="24" color="#f54040" />
        </div>
    </van-dialog>
    <!-- <div style="position:fixed;top:0;bottom:0;left:0;right:0;background: #000;" v-if="show"></div> -->
</template>
<script>
import { Dialog, Icon } from "vant";
import base from "../lib/base";
import Tool from '@/common/tool'
export default {
    mixins: [base],
    name: "FunctionalDialog",
    components: {
        [Dialog.Component.name]: Dialog.Component,
        VanIcon: Icon,
    },
    data() {
        return {
            show: false,
            title: "",
            showConfirmButton: false,
            showCancelButton: false,
            showRejectButton: false,
            beforeClose: null,
            confirmButtonText: "",
            rejectButtonText: "",
            message:'',
            messageAlign:'center',
            confirm:null,
            reject:null,
            cancel:null,
            close:null,
            closeOnClickOverlay:false,
            closeOnPopstate:true,
            currentDialogId:0,
            forbiddenClose:false

        };
    },

    mounted() {},
    computed: {},
    methods: {
        showDialog:Tool.debounce(async function ({
            showRejectButton = false,
            showConfirmButton = true,
            showCancelButton = true,
            confirmButtonText = "",
            rejectButtonText = "",
            title = "",
            beforeClose = null,
            confirm = null,
            reject=null,
            cancel=null,
            message='',
            close=null,
            messageAlign='center',
            closeOnClickOverlay=false,
            closeOnPopstate = true,
            forbiddenClose = false
        } = {}) {
            if(this.show === true){
                return
            }
            this.show = true;
            this.title = title||this.lang.tip_title;
            this.showConfirmButton = showConfirmButton;
            this.showRejectButton = showRejectButton;
            this.showCancelButton = showCancelButton;
            this.confirmButtonText = confirmButtonText||this.lang.confirm_txt;
            this.rejectButtonText = rejectButtonText||this.lang.cancel_btn;
            this.beforeClose = beforeClose;
            this.message = message
            this.messageAlign = messageAlign
            this.confirm = confirm;
            this.reject = reject;
            this.cancel = cancel;
            this.closeOnClickOverlay = closeOnClickOverlay
            this.closeOnPopstate = closeOnPopstate
            this.forbiddenClose = forbiddenClose
            this.close = close;
            this.show = true;
            this.currentDialogId = Tool.genID(3)
            this.$root.currentDialogList.push({
                id:this.currentDialogId,
                el:this
            })
        },500,true),
        handleClose() {
            if (this.close) {
                this.close();
                this.show = false
            }
        },
        handleBeforeClose(action, done) {
            if (this.beforeClose) {
                this.beforeClose(action, done);
            } else {
                done();
            }
        },
        handleConfirm(){
            this.closeDialog()
            if (this.confirm) {
                this.confirm();
            }
        },
        handleReject(){
            this.closeDialog()
            if (this.reject) {
                this.reject();
            }
        },
        handelCancelButton(){
            this.closeDialog()
            if (this.cancel) {
                this.cancel();
            }
        },
        closeDialog() {
            this.show = false;
            this.$root.currentDialogList = this.$root.currentDialogList.filter(item=>item.id!==this.currentDialogId)
        },
        checkCanClose(){
            return !this.forbiddenClose
        },
        checkCanCloseOnPopstate(){
            return this.closeOnPopstate&&!this.forbiddenClose
        },
        checkIsShow(){
            return this.show
        }
    },
};
</script>
<style lang="scss">
.common_dialog {
    min-width: 300px;
    width: 80%;
    max-width: 540px;
    border-radius: 5px;
    .common_dialog-title {
        font-size: 0.82rem; // 18px = 0.82rem
        font-weight: 600;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 2.45rem; // 54px = 2.45rem
        line-height: 1.4;
        padding: 0.55rem 0.91rem 0.36rem 0.91rem; // 12px 20px 8px 20px
        text-align: center;
        word-break: break-word;
        white-space: normal;
    }
    .common_dialog-content {
        min-height: 2rem; // 44px = 2rem
        padding: 0.55rem 1rem; // 12px 22px
        font-size: 0.73rem; // 16px = 0.73rem
        .common_dialog-message{
            word-break: break-word;
        }
    }

    .cancel_button {
        position: absolute;
        top: 0;
        z-index: 10;
        right: 0;
    }
    .align_left{
        text-align: left;
    }
    .align_center{
        text-align: center;
    }
    .align_right{
        text-align: right;
    }
    .van-checkbox-group {
        position: relative !important;
        border-radius: 0rem !important;

        .van-checkbox {
            padding: 0px !important;
        }
    }
    .van-dialog__footer {
        .van-button {
            color: #00c59d;
        }
    }
    .van-picker__cancel,
    .van-picker__confirm {
        font-size: 0.73rem !important;
        line-height: 1.2 !important;
        padding: 0 1rem !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        min-width: 3.18rem !important;
        text-align: center !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    // 修复van-picker标题显示，最多两行，超过显示省略号
    .van-picker__title {
        font-size: 0.73rem !important;
        font-weight: 500 !important;
        line-height: 1.4 !important;
        color: #323233 !important;
        text-align: center !important;
        padding: 0 0.5rem !important;
        max-height: 2.05rem !important; // 约两行的高度 (0.73rem * 1.4 * 2 ≈ 2.05rem)
        overflow: hidden !important;
        display: -webkit-box !important;
        -webkit-box-orient: vertical !important;
        -webkit-line-clamp: 2 !important;
        line-clamp: 2 !important; // 标准属性，用于兼容性
        word-break: break-word !important;
        white-space: normal !important;
    }
}
</style>
