<template>
    <van-dialog
        v-model="show"
        v-bind="$attrs"
        class="common_dialog"
        :show-cancel-button="showRejectButton"
        :show-confirm-button="showConfirmButton"
        :confirm-button-text="confirmButtonText||lang.confirm_txt"
        :cancel-button-text="rejectButtonText||lang.cancel_btn"
        :message-align="messageAlign"
        @confirm="handleConfirm"
        @cancel="handleReject"
        :closeOnClickOverlay="closeOnClickOverlay"
        :close-on-popstate="false"
    >
        <div class="common_dialog-title">{{ title||lang.tip_title }}</div>
        <div class="common_dialog-content" :class="[`align_${messageAlign}`]">
            <slot><p v-html="message" class="common_dialog-message"></p></slot>
        </div>
        <div class="cancel_button" v-if="showCancelButton" @click="handelCancelButton">
            <van-icon name="close" size="24" color="#f54040" />
        </div>
    </van-dialog>
    <!-- <div style="position:fixed;top:0;bottom:0;left:0;right:0;background: #000;" v-if="show"></div> -->
</template>
<script>
import { Dialog, Icon } from "vant";
import Tool from '@/common/tool'
export default {
    model: {
        prop: "value",
        event: "change",
    },
    name: "CommonDialog",
    props:{
        value: {
            type: Boolean,
            default: false,
        },
        showConfirmButton:{
            type:Boolean,
            default:true
        },
        showCancelButton:{
            type:Boolean,
            default:true
        },
        showRejectButton:{
            type:Boolean,
            default:false,
        },
        closeOnClickOverlay:{
            type:Boolean,
            default:false
        },
        title:{
            type:String,
            default:''
        },
        confirmButtonText:{
            type:String,
            default:''
        },
        rejectButtonText:{
            type:String,
            default:''
        },
        messageAlign:{
            type:String,
            default:'center'
        },
        message:{
            type:String,
            default:''
        },
        closeOnPopstate:{
            type:Boolean,
            default:true
        },
        forbiddenClose:{
            type:Boolean,
            default:false
        }

    },
    components: {
        [Dialog.Component.name]: Dialog.Component,
        VanIcon: Icon,
    },
    computed:{
        lang(){
            return this.$store.state.language
        }
    },
    watch: {
        value: {
            handler(val) {
                this.show = val;
                if(val){
                    this.currentDialogId = Tool.genID(3)
                    this.$root.currentDialogList.push({
                        id:this.currentDialogId,
                        el:this
                    })
                }else{
                    this.$root.currentDialogList = this.$root.currentDialogList.filter(item=>item.id!==this.currentDialogId)
                }
            }
        },
        show: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    data() {
        return {
            show: false,
            currentDialogId:0
        };
    },
    created(){

    },
    mounted() {


    },
    methods: {
        showDialog(){
            this.show = true
        },
        handleConfirm(){
            this.$emit('confirm')
        },
        handleReject(){
            this.$emit('reject')
        },
        handelCancelButton(){
            this.closeDialog()
            this.$emit('cancel')
        },
        closeDialog() {
            this.show = false;
        },
        checkCanClose(){
            return !this.forbiddenClose
        },
        checkCanCloseOnPopstate(){
            return this.closeOnPopstate&&!this.forbiddenClose
        },
        checkIsShow(){
            return this.show
        }
    },
};
</script>
<style lang="scss">
.common_dialog {
    min-width: 300px;
    width: 80%;
    max-width: 540px;
    border-radius: 5px;
    .common_dialog-title {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 54px;
        line-height: 1.4;
        padding: 12px 20px 8px 20px;
        text-align: center;
        word-break: break-word;
        white-space: normal;
    }
    .common_dialog-content {
        min-height: 44px;
        padding: 12px 22px;
        font-size: 16px;
        .common_dialog-message{
            word-break: break-word;
        }
    }
    .van-dialog__footer {
        padding: 16px !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 12px !important;

        .van-button {
            color: #00c59d;
            font-size: 16px !important;
            line-height: 1.4 !important;
            padding: 8px 16px !important;
            min-height: 44px !important;
            border-radius: 6px !important;
            flex: 1 !important;
            white-space: nowrap !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;

            &.van-button--primary {
                background-color: #00c59d !important;
                border-color: #00c59d !important;
                color: #fff !important;
            }

            &.van-button--default {
                background-color: #fff !important;
                border-color: #dcdee0 !important;
                color: #323233 !important;
            }
        }
    }
    .cancel_button {
        position: absolute;
        top: 0;
        z-index: 10;
        right: 0;
    }
    .align_left{
        text-align: left;
    }
    .align_center{
        text-align: center;
    }
    .align_right{
        text-align: right;
    }

    .van-checkbox-group {
        position: relative !important;
        border-radius :0rem !important;

         .van-checkbox {
            padding: 0px !important;
        }
    }
}
</style>
